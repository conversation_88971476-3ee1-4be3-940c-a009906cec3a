// Type definitions for AWS Lambda Authorizer

export interface APIGatewayAuthorizerEvent {
  type: string;
  authorizationToken?: string;
  methodArn: string;
  headers?: Record<string, string>;
  pathParameters?: Record<string, string> | null;
  queryStringParameters?: Record<string, string> | null;
  requestContext?: {
    accountId: string;
    apiId: string;
    httpMethod: string;
    requestId: string;
    resourceId: string;
    resourcePath: string;
    stage: string;
  };
}

export interface PolicyStatement {
  Action: string;
  Effect: 'Allow' | 'Deny';
  Resource: string | string[];
}

export interface PolicyDocument {
  Version: string;
  Statement: PolicyStatement[];
}

export interface AuthorizerResponse {
  principalId: string;
  policyDocument: PolicyDocument;
  context?: Record<string, string | number | boolean>;
}

export interface JWTClaims {
  sub: string;
  iss: string;
  aud: string | string[];
  exp: number;
  iat: number;
  customer?: string;
  roles?: string[];
  [key: string]: any;
}

export interface AuthorizerConfig {
  auth0Domain: string;
  auth0Audience: string;
  cacheMaxAge?: number;
  jwksRequestsPerMinute?: number;
}

export interface ParsedMethodArn {
  stage: string;
  apiPath: string;
  httpMethod: string;
  resource: string;
}

export interface AuthorizationResult {
  isAuthorized: boolean;
  allowedResources: string[];
  reason?: string;
}
